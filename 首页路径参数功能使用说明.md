# 首页路径参数数据加载功能使用说明

## 🎯 功能概述

现在您的trame应用支持通过首页URL参数直接加载数据！只需在访问首页时添加`path`参数，应用就会自动清空现有数据并加载指定的文件或文件夹。

## ✨ 核心特性

### 🏠 首页集成
- **无缝集成**：数据加载功能直接集成到首页访问中
- **自动加载**：打开页面时自动在后台加载数据
- **正常访问**：不影响正常的首页访问体验

### 📁 双路径支持
- **单文件加载**：支持加载单个数据文件
- **文件夹加载**：支持批量加载文件夹中的所有支持格式文件
- **智能过滤**：自动识别和加载支持的医学影像格式

### 🔄 兼容性保证
- **API兼容**：原有的`/api/load_data`接口仍然可用
- **向后兼容**：不影响现有的功能和使用方式

## 📝 使用方法

### 基本语法
```
http://your-server:port/?path=/your/data/path
```

### 使用示例

#### 1. 加载单个文件
```bash
# 加载VTP文件
http://localhost:8080/?path=/data/medical/vessel.vtp

# 加载STL文件
http://localhost:8080/?path=/data/models/brain.stl

# 加载医学影像
http://localhost:8080/?path=/data/scans/ct_scan.vti
```

#### 2. 加载文件夹
```bash
# 加载整个文件夹
http://localhost:8080/?path=/data/medical_scans/

# 加载项目文件夹
http://localhost:8080/?path=/projects/patient_001/
```

#### 3. 正常访问（无数据加载）
```bash
# 正常首页访问
http://localhost:8080/
```

### URL编码支持
对于包含特殊字符的路径，会自动进行URL解码：
```bash
# 包含空格的路径
http://localhost:8080/?path=/data/medical%20scans/patient%20001.vtp
```

## 🔧 支持的文件格式

- `.vtp` - VTK PolyData文件
- `.stl` - STL表面网格文件
- `.vti` - VTK ImageData文件
- `.mha` - MetaImage文件
- `.nii` - NIfTI文件
- `.nii.gz` - 压缩的NIfTI文件
- `.nrrd` - NRRD文件

## 🚀 启动和测试

### 1. 启动应用
```bash
python examples/CTP4D.py --port 8080
```

### 2. 测试功能
```bash
# 运行完整测试
python test_homepage_loading.py 8080

# 测试API兼容性
python test_http_api.py /path/to/test/data 8080
```

## 💡 实际使用场景

### 场景1：从其他系统跳转
```javascript
// 从医院信息系统跳转到可视化应用
const patientDataPath = "/data/patients/12345/ct_scan.vti";
const visualizationUrl = `http://visualization-server:8080/?path=${encodeURIComponent(patientDataPath)}`;
window.open(visualizationUrl, '_blank');
```

### 场景2：邮件链接分享
```html
<!-- 在邮件中分享数据可视化链接 -->
<a href="http://your-server:8080/?path=/shared/research/dataset_001/">
  查看研究数据集可视化
</a>
```

### 场景3：工作流集成
```bash
# 在脚本中自动打开可视化
SCAN_PATH="/data/daily_scans/$(date +%Y%m%d)/"
xdg-open "http://localhost:8080/?path=${SCAN_PATH}"
```

## 🔍 工作原理

### 技术实现
1. **中间件拦截**：使用aiohttp中间件拦截首页请求
2. **参数检测**：检查URL中是否包含`path`参数
3. **数据加载**：在后台同步加载指定的数据
4. **页面渲染**：继续正常的页面渲染流程

### 执行流程
```
用户访问 /?path=/data/file.vtp
    ↓
中间件检测到path参数
    ↓
清空现有数据
    ↓
加载新数据（文件或文件夹）
    ↓
返回正常的HTML页面
    ↓
用户看到加载了数据的可视化界面
```

## 📊 功能对比

| 功能 | 原API接口 | 首页参数 |
|------|-----------|----------|
| 数据加载 | ✅ | ✅ |
| 文件夹支持 | ✅ | ✅ |
| 错误处理 | ✅ | ✅ |
| 返回格式 | JSON | HTML页面 |
| 使用场景 | API调用 | 直接访问 |
| 用户体验 | 需要额外处理 | 一步到位 |

## 🛠️ 故障排除

### 常见问题

#### 1. 数据没有加载
- **检查路径**：确保文件或文件夹路径正确
- **检查格式**：确保文件格式在支持列表中
- **查看日志**：检查服务器控制台的加载日志

#### 2. 页面访问失败
- **检查服务器**：确保trame应用正在运行
- **检查端口**：确认端口号正确
- **检查URL编码**：特殊字符需要URL编码

#### 3. 部分文件加载失败
- **查看详细日志**：服务器会显示每个文件的加载状态
- **检查文件权限**：确保应用有读取文件的权限
- **验证文件格式**：确保文件是有效的医学影像格式

### 调试技巧
```bash
# 查看服务器日志
python examples/CTP4D.py --port 8080

# 在日志中查找以下信息：
# 🏠 首页检测到路径参数: /your/path
# ✅ 首页数据加载成功: 全部成功：加载了 X 个文件
```

## 🎉 总结

首页路径参数功能为您的trame应用提供了更加便捷的数据加载方式：

- **用户友好**：一个URL即可完成数据加载和页面访问
- **系统集成**：便于与其他系统进行集成
- **向后兼容**：不影响现有功能
- **功能完整**：支持所有原有的数据加载特性

现在您可以通过简单的URL分享，让用户直接访问预加载了特定数据的可视化界面！🚀
