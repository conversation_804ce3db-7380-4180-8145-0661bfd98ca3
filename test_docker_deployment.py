#!/usr/bin/env python3
"""
测试Docker部署的脚本
"""

import requests
import time
import sys
import subprocess
import os
import tempfile

def run_command(cmd, timeout=30):
    """运行shell命令"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令超时"
    except Exception as e:
        return False, "", str(e)

def check_docker():
    """检查Docker是否可用"""
    print("🔍 检查Docker环境...")
    
    success, stdout, stderr = run_command("docker --version")
    if not success:
        print("❌ Docker未安装或不可用")
        return False
    print(f"✅ Docker版本: {stdout.strip()}")
    
    success, stdout, stderr = run_command("docker-compose --version")
    if not success:
        print("❌ Docker Compose未安装或不可用")
        return False
    print(f"✅ Docker Compose版本: {stdout.strip()}")
    
    return True

def create_test_data():
    """创建测试数据"""
    print("📁 创建测试数据...")
    
    # 创建数据目录
    data_dir = "/tmp/test_ctpdata"
    os.makedirs(data_dir, exist_ok=True)
    
    # 创建测试文件
    test_files = [
        "vessel_001.vtp",
        "vessel_002.vtp",
        "brain_scan.vti"
    ]
    
    for filename in test_files:
        file_path = os.path.join(data_dir, filename)
        with open(file_path, 'w') as f:
            f.write(f"# Test file: {filename}\n")
        print(f"  ✅ 创建测试文件: {filename}")
    
    return data_dir

def test_docker_deployment():
    """测试Docker部署"""
    print("🐳 测试Docker部署...")
    
    # 检查Docker环境
    if not check_docker():
        return False
    
    # 创建测试数据
    test_data_dir = create_test_data()
    
    try:
        # 修改docker-compose文件中的数据目录
        print("📝 准备Docker配置...")
        
        # 读取原始配置
        with open('dev.yml', 'r') as f:
            compose_content = f.read()
        
        # 创建测试配置
        test_compose_content = compose_content.replace(
            '/data/ctpdata:/data/ctpdata',
            f'{test_data_dir}:/data/ctpdata'
        )
        
        # 写入测试配置
        with open('test-dev.yml', 'w') as f:
            f.write(test_compose_content)
        
        print("🚀 启动Docker容器...")
        
        # 停止可能存在的容器
        run_command("docker-compose -f test-dev.yml down", timeout=60)
        
        # 启动容器
        success, stdout, stderr = run_command(
            "docker-compose -f test-dev.yml up -d", 
            timeout=120
        )
        
        if not success:
            print(f"❌ 容器启动失败: {stderr}")
            return False
        
        print("✅ 容器启动成功")
        
        # 等待应用启动
        print("⏳ 等待应用启动...")
        max_wait = 60
        wait_time = 0
        
        while wait_time < max_wait:
            try:
                response = requests.get('http://localhost:8080/', timeout=5)
                if response.status_code == 200:
                    print("✅ 应用启动成功")
                    break
            except:
                pass
            
            time.sleep(2)
            wait_time += 2
            print(f"  等待中... ({wait_time}/{max_wait}秒)")
        
        if wait_time >= max_wait:
            print("❌ 应用启动超时")
            return False
        
        # 测试基本功能
        print("🧪 测试应用功能...")
        
        # 测试首页
        try:
            response = requests.get('http://localhost:8080/', timeout=10)
            if response.status_code == 200:
                print("✅ 首页访问正常")
            else:
                print(f"❌ 首页访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 首页访问异常: {e}")
            return False
        
        # 测试路径参数功能
        try:
            response = requests.get('http://localhost:8080/?path=/data/ctpdata', timeout=10)
            if response.status_code == 200:
                print("✅ 路径参数功能正常")
            else:
                print(f"❌ 路径参数功能失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 路径参数功能异常: {e}")
            return False
        
        # 检查容器日志
        print("📋 检查容器日志...")
        success, stdout, stderr = run_command(
            "docker-compose -f test-dev.yml logs --tail=20", 
            timeout=30
        )
        
        if success and "首页路径参数功能已设置" in stdout:
            print("✅ 容器日志正常")
        else:
            print("⚠️  容器日志可能有问题")
            if stdout:
                print(f"日志输出: {stdout[-500:]}")  # 显示最后500字符
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        return False
    
    finally:
        # 清理
        print("🧹 清理测试环境...")
        run_command("docker-compose -f test-dev.yml down", timeout=60)
        
        # 删除测试文件
        if os.path.exists('test-dev.yml'):
            os.remove('test-dev.yml')
        
        # 删除测试数据
        import shutil
        if os.path.exists(test_data_dir):
            shutil.rmtree(test_data_dir)

def main():
    """主函数"""
    print("=" * 60)
    print("🐳 Docker部署测试")
    print("=" * 60)
    
    if test_docker_deployment():
        print("\n🎉 Docker部署测试通过！")
        print("\n💡 使用方法:")
        print("   1. 启动: docker-compose -f dev.yml up -d")
        print("   2. 访问: http://localhost:8080/")
        print("   3. 停止: docker-compose -f dev.yml down")
        return 0
    else:
        print("\n❌ Docker部署测试失败！")
        print("\n🔧 故障排除:")
        print("   1. 检查Docker是否正常运行")
        print("   2. 检查8080端口是否被占用")
        print("   3. 检查requirements.txt是否存在")
        print("   4. 查看容器日志: docker-compose -f dev.yml logs")
        return 1

if __name__ == "__main__":
    sys.exit(main())
