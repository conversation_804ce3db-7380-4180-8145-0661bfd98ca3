# 使用Python 3.8 slim镜像作为基础镜像
FROM python:3.8-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量，解决线程和pip问题
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_PROGRESS_BAR=off \
    PIP_TIMEOUT=1000 \
    OPENBLAS_NUM_THREADS=1 \
    NUMEXPR_NUM_THREADS=1 \
    MKL_NUM_THREADS=1 \
    OMP_NUM_THREADS=1 \
    VECLIB_MAXIMUM_THREADS=1

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        libx11-6 \
        libxrender1 \
        libxtst6 \
        libgl1-mesa-glx \
        libglib2.0-0 \
        python3-tk \
        tk-dev \
        libegl1 \
        libosmesa6 \
        libosmesa6-dev \
        libgles2-mesa-dev \
        build-essential \
        curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 复制requirements文件
COPY requirements.txt ./

# 升级pip到稳定版本，避免线程问题
RUN python -m pip install --no-cache-dir pip==23.3.2

# 分批安装依赖，避免并发问题
RUN pip install --no-cache-dir --timeout=1000 numpy>=1.12.4

# 安装其他依赖
RUN pip install --no-cache-dir --timeout=1000 -r requirements.txt

# 复制应用代码
COPY mipf/ ./mipf/
COPY examples/ ./examples/

# 暴露端口
EXPOSE 8080

# 启动CTP4D应用
CMD ["python", "examples/CTP4D.py", "--server", "--host", "0.0.0.0", "--port", "8080"]
