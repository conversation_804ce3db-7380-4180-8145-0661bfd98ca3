#!/usr/bin/env python3
"""
测试HTTP API的脚本
用于验证自定义HTTP接口是否正常工作
"""

import requests
import json
import time
import sys
from urllib.parse import quote

def test_load_data_api(base_url="http://localhost:8080", path="/path/to/your/data"):
    """
    测试加载数据的HTTP API（支持文件或文件夹）

    Args:
        base_url: trame服务器的基础URL
        path: 要加载的数据文件路径或文件夹路径
    """

    # 测试GET请求
    print("=== 测试GET请求 ===")
    try:
        # URL编码路径
        encoded_path = quote(path)
        get_url = f"{base_url}/api/load_data?path={encoded_path}"

        print(f"发送GET请求到: {get_url}")
        response = requests.get(get_url, timeout=10)

        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ GET请求成功")
                # 显示详细信息
                if 'loaded_files' in result:
                    print(f"   加载的文件数量: {len(result['loaded_files'])}")
                    print(f"   路径类型: {result.get('path_type', 'unknown')}")
            else:
                print(f"❌ GET请求失败: {result.get('message')}")
        else:
            print(f"❌ GET请求失败，状态码: {response.status_code}")

    except requests.exceptions.RequestException as e:
        print(f"❌ GET请求异常: {str(e)}")

    print("\n" + "="*50 + "\n")

    # 测试POST请求
    print("=== 测试POST请求 ===")
    try:
        post_url = f"{base_url}/api/load_data"
        post_data = {"path": path}

        print(f"发送POST请求到: {post_url}")
        print(f"请求数据: {json.dumps(post_data, indent=2)}")

        response = requests.post(
            post_url,
            json=post_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )

        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ POST请求成功")
                # 显示详细信息
                if 'loaded_files' in result:
                    print(f"   加载的文件数量: {len(result['loaded_files'])}")
                    print(f"   路径类型: {result.get('path_type', 'unknown')}")
                    if result.get('failed_files'):
                        print(f"   失败的文件数量: {len(result['failed_files'])}")
            else:
                print(f"❌ POST请求失败: {result.get('message')}")
        else:
            print(f"❌ POST请求失败，状态码: {response.status_code}")

    except requests.exceptions.RequestException as e:
        print(f"❌ POST请求异常: {str(e)}")

def test_server_connection(base_url="http://localhost:8080"):
    """测试服务器连接"""
    print("=== 测试服务器连接 ===")
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✅ 服务器连接正常，状态码: {response.status_code}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        return False

def main():
    """主函数"""
    print("HTTP API 测试工具")
    print("="*50)

    # 默认参数
    base_url = "http://localhost:8080"

    # 从命令行参数获取路径
    if len(sys.argv) > 1:
        test_path = sys.argv[1]
    else:
        # 使用示例路径
        test_path = "/path/to/your/data"
        print(f"⚠️  未提供路径，使用示例路径: {test_path}")
        print("   使用方法: python test_http_api.py /path/to/your/file_or_folder")

    print(f"服务器地址: {base_url}")
    print(f"测试路径: {test_path}")
    print("   支持文件或文件夹路径")
    print()

    # 测试服务器连接
    if not test_server_connection(base_url):
        print("请确保trame服务器正在运行在 http://localhost:8080")
        return

    print()

    # 等待一下让服务器完全启动
    time.sleep(1)

    # 测试API
    test_load_data_api(base_url, test_path)

    print("\n" + "="*50)
    print("测试完成")

if __name__ == "__main__":
    main()
