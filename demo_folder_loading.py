#!/usr/bin/env python3
"""
演示文件夹加载功能的脚本
展示如何通过HTTP API批量加载文件夹中的数据文件
"""

import os
import tempfile
import json

def create_demo_files():
    """创建演示用的测试文件"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="trame_demo_")
    print(f"📁 创建演示文件夹: {temp_dir}")
    
    # 创建一些示例文件（空文件，仅用于演示）
    demo_files = [
        "vessel_001.vtp",
        "vessel_002.vtp", 
        "brain_scan.vti",
        "surface_mesh.stl",
        "volume_data.mha",
        "readme.txt",  # 不支持的格式
        "config.json"  # 不支持的格式
    ]
    
    created_files = []
    for filename in demo_files:
        file_path = os.path.join(temp_dir, filename)
        with open(file_path, 'w') as f:
            f.write(f"# Demo file: {filename}\n")
        created_files.append(file_path)
        print(f"  ✅ 创建文件: {filename}")
    
    return temp_dir, created_files

def demo_api_calls(folder_path):
    """演示API调用"""
    print(f"\n🚀 演示HTTP API调用")
    print(f"文件夹路径: {folder_path}")
    

    
    # 演示curl命令
    print(f"\n📋 curl命令示例:")
    print(f"# GET请求:")
    print(f'curl "http://localhost:8080/api/load_data?path={folder_path}"')
    
    print(f"\n# POST请求:")
    post_data = {"path": folder_path}
    print(f"curl -X POST http://localhost:8080/api/load_data \\")
    print(f"  -H 'Content-Type: application/json' \\")
    print(f"  -d '{json.dumps(post_data)}'")
    
    # 演示Python代码
    print(f"\n🐍 Python代码示例:")
    python_code = f'''
import requests
import json

folder_path = "{folder_path}"

response = requests.post(
    "http://localhost:8080/api/load_data",
    json={{"path": folder_path}},
    headers={{'Content-Type': 'application/json'}}
)

if response.status_code == 200:
    result = response.json()
    if result['success']:
        print(f"文件夹加载成功！")
        print(f"路径类型: {{result['path_type']}}")
        print(f"成功加载: {{len(result['loaded_files'])}} 个文件")
        print(f"失败文件: {{len(result['failed_files'])}} 个")
        
        for file in result['loaded_files']:
            print(f"  ✅ {{file}}")
            
        for failed in result['failed_files']:
            print(f"  ❌ {{failed['path']}}: {{failed['error']}}")
    else:
        print(f"加载失败: {{result['message']}}")
else:
    print(f"请求失败: {{response.status_code}}")
'''
    print(python_code)

def demo_expected_response(folder_path):
    """演示预期的响应"""
    print(f"\n📄 预期的API响应:")
    
    # 模拟响应
    expected_response = {
        "success": True,
        "message": "全部成功：加载了 4 个文件",
        "loaded_files": [
            os.path.join(folder_path, "brain_scan.vti"),
            os.path.join(folder_path, "surface_mesh.stl"),
            os.path.join(folder_path, "vessel_001.vtp"),
            os.path.join(folder_path, "vessel_002.vtp"),
            os.path.join(folder_path, "volume_data.mha")
        ],
        "failed_files": [],
        "total_found": 5,
        "path_type": "folder"
    }
    
    print(json.dumps(expected_response, indent=2, ensure_ascii=False))

def cleanup_demo_files(folder_path):
    """清理演示文件"""
    import shutil
    try:
        shutil.rmtree(folder_path)
        print(f"\n🧹 已清理演示文件夹: {folder_path}")
    except Exception as e:
        print(f"⚠️  清理文件夹失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 文件夹加载功能演示")
    print("=" * 60)
    
    print("\n📋 功能说明：")
    print("• 支持传入文件夹路径，自动扫描并加载支持格式的文件")
    print("• 智能过滤：只加载支持的医学影像格式")
    print("• 批量处理：一次请求处理多个文件")
    print("• 详细反馈：提供成功和失败文件的详细信息")
    
    print("\n🔧 支持的文件格式：")
    formats = [".vtp", ".stl", ".vti", ".mha", ".nii", ".nii.gz", ".nrrd"]
    print("   " + ", ".join(formats))
    
    # 创建演示文件
    folder_path, created_files = create_demo_files()
    
    try:
        # 演示API调用
        demo_api_calls(folder_path)
        
        # 演示预期响应
        demo_expected_response(folder_path)
        
        print(f"\n💡 测试步骤：")
        print(f"1. 启动trame应用：python examples/CTP4D.py")
        print(f"2. 运行测试脚本：python test_http_api.py {folder_path}")
        print(f"3. 观察加载结果")
        
        print(f"\n📊 预期结果：")
        print(f"• 成功加载 5 个支持格式的文件")
        print(f"• 自动忽略 2 个不支持格式的文件")
        print(f"• 返回详细的加载状态信息")
        
    finally:
        # 清理演示文件
        cleanup_demo_files(folder_path)
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")

if __name__ == "__main__":
    main()
