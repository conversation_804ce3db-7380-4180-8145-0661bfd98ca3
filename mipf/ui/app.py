from mipf.core.render_window import *
from mipf.core.data import *
from mipf.core.utils import *
from mipf.core.settings import *
from mipf.ui.data import *
from mipf.ui.engine import *
from abc import ABC, abstractmethod

class AppBase(ABC):
    def __init__(self, server, app_name="Undefined"):
        self.server = server
        self.app_name = app_name
        self.data_storage = DataStorage()

    @abstractmethod
    def setupui(self):
        pass

    @property
    def state(self):
        return self.server.state

    @property
    def ctrl(self):
        return self.server.controller

    def clear_all_data(self):
        """清空所有数据节点"""
        # 获取所有节点ID的副本，避免在迭代时修改字典
        node_ids = list(self.data_storage.nodes.keys())

        # 逐个删除节点
        for node_id in node_ids:
            if node_id in self.data_storage.nodes:
                self.data_storage.remove_node(node_id)

        # 请求更新渲染
        render_window_manager.request_update_all()
        if self.server.protocol:
            self.ctrl.view_update()

        print("所有数据已清空")

    def load_data_from_path(self, file_path: str, clear_existing=True):
        """从路径加载数据，可选择是否清空现有数据"""
        import os

        if clear_existing:
            self.clear_all_data()

        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False

        try:
            # 使用文件名作为节点名称
            node_name = os.path.basename(file_path)
            self.load(file_path, node_name)
            print(f"成功加载文件: {file_path}")
            return True
        except Exception as e:
            print(f"加载文件失败: {file_path}, 错误: {str(e)}")
            return False

    def load(self, filename: str, name="undefined"):
        if filename.endswith('nii') or filename.endswith('nii.gz') or \
                filename.endswith('vti') or filename.endswith('mha') or \
                filename.endswith('nrrd'):
            node = import_image_file(filename, name)
            self.data_storage.add_node(node)
            render_window_manager.request_update_all()
            if self.server.protocol:
                self.ctrl.reset_camera()
                self.ctrl.view_update()
        elif filename.endswith('vtp') or filename.endswith('stl'):
            node = import_surface_file(filename, name)
            self.data_storage.add_node(node)
            render_window_manager.request_update_all()
            if self.server.protocol:
                self.ctrl.reset_camera()
                self.ctrl.view_update()
        else:
            print("Not a supported file ",filename)