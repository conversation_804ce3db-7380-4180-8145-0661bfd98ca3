# HTTP API 使用说明

## 功能概述

本功能实现了通过HTTP接口远程控制trame应用加载数据的能力。另一个服务可以通过HTTP请求跳转到当前服务，传入文件路径参数，当前服务会清空现有数据并重新加载指定的数据文件。

## trame框架自定义接口支持

**是的，trame框架支持自定义HTTP接口！**

trame框架通过`on_server_bind`生命周期回调提供了添加自定义HTTP路由的能力。当WSLinkServer绑定到trame时，可以通过这个回调访问底层的服务器实例并添加自定义路由。

## API接口详情

### 端点地址
```
GET/POST /api/load_data
```

### 请求方式

#### 1. GET请求
```bash
curl "http://localhost:8080/api/load_data?path=/path/to/your/file.vtp"
```

#### 2. POST请求
```bash
curl -X POST http://localhost:8080/api/load_data \
  -H "Content-Type: application/json" \
  -d '{"path": "/path/to/your/file.vtp"}'
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| path | string | 是 | 要加载的数据文件的完整路径 |

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "message": "成功加载数据: /path/to/your/file.vtp"
}
```

#### 失败响应
```json
{
  "success": false,
  "message": "错误描述信息"
}
```

### HTTP状态码

- `200`: 请求成功
- `400`: 请求参数错误（缺少path参数）
- `500`: 服务器内部错误（文件不存在、加载失败等）

## 支持的文件格式

当前支持以下数据文件格式：
- `.vtp` - VTK PolyData文件
- `.stl` - STL表面网格文件
- `.vti` - VTK ImageData文件
- `.mha` - MetaImage文件
- `.nii` - NIfTI文件
- `.nii.gz` - 压缩的NIfTI文件
- `.nrrd` - NRRD文件

## 使用示例

### 1. 启动trame应用
```bash
cd /path/to/your/project
python examples/CTP4D.py
```

应用将在 `http://localhost:8080` 启动。

### 2. 从另一个服务调用API

#### Python示例
```python
import requests
import json

# 要加载的文件路径
file_path = "/data/medical/vessel.vtp"

# 发送POST请求
response = requests.post(
    "http://localhost:8080/api/load_data",
    json={"path": file_path},
    headers={'Content-Type': 'application/json'}
)

if response.status_code == 200:
    result = response.json()
    if result['success']:
        print("数据加载成功！")
    else:
        print(f"加载失败: {result['message']}")
else:
    print(f"请求失败: {response.status_code}")
```

#### JavaScript示例
```javascript
const loadData = async (filePath) => {
  try {
    const response = await fetch('http://localhost:8080/api/load_data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ path: filePath })
    });
    
    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log('数据加载成功！');
    } else {
      console.error('加载失败:', result.message);
    }
  } catch (error) {
    console.error('请求异常:', error);
  }
};

// 使用示例
loadData('/data/medical/vessel.vtp');
```

### 3. 测试API

使用提供的测试脚本：
```bash
python test_http_api.py /path/to/your/test/file.vtp
```

## 功能特性

1. **数据清空**: 加载新数据前会自动清空当前所有数据节点
2. **错误处理**: 完善的错误处理和响应机制
3. **路径验证**: 自动验证文件路径是否存在
4. **URL解码**: 支持URL编码的文件路径
5. **多种请求方式**: 支持GET和POST两种请求方式
6. **JSON响应**: 统一的JSON格式响应

## 注意事项

1. **文件路径**: 确保提供的文件路径在服务器上存在且可访问
2. **文件格式**: 只支持列出的数据文件格式
3. **网络安全**: 在生产环境中考虑添加认证和授权机制
4. **错误处理**: 调用方应该处理各种可能的错误情况
5. **并发访问**: 当前实现不支持并发数据加载操作

## 扩展功能

可以基于此实现进一步扩展：
- 添加用户认证
- 支持批量文件加载
- 添加数据加载进度反馈
- 支持更多文件格式
- 添加数据预览功能
