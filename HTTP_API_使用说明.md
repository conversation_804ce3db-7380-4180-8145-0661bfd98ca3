# HTTP API 使用说明

## 功能概述

本功能实现了通过HTTP接口远程控制trame应用加载数据的能力。另一个服务可以通过HTTP请求跳转到当前服务，传入文件路径或文件夹路径参数，当前服务会清空现有数据并重新加载指定的数据。

### 🆕 新增功能
- **支持文件夹加载**：可以传入文件夹路径，自动扫描并加载其中所有支持格式的文件
- **批量处理**：一次请求可以加载多个文件
- **智能过滤**：自动识别和过滤支持的文件格式
- **详细反馈**：提供加载成功和失败的详细信息

## trame框架自定义接口支持

**是的，trame框架支持自定义HTTP接口！**

trame框架通过`on_server_bind`生命周期回调提供了添加自定义HTTP路由的能力。当WSLinkServer绑定到trame时，可以通过这个回调访问底层的服务器实例并添加自定义路由。

## API接口详情

### 端点地址
```
GET/POST /api/load_data
```

### 请求方式

#### 1. GET请求
```bash
curl "http://localhost:8080/api/load_data?path=/path/to/your/file.vtp"
```

#### 2. POST请求
```bash
curl -X POST http://localhost:8080/api/load_data \
  -H "Content-Type: application/json" \
  -d '{"path": "/path/to/your/file.vtp"}'
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| path | string | 是 | 要加载的数据文件路径或文件夹路径 |

**路径类型支持：**
- **单个文件**：`/path/to/file.vtp`
- **文件夹**：`/path/to/folder/` - 会自动扫描文件夹中的所有支持格式文件

### 响应格式

#### 成功响应

**单个文件加载成功：**
```json
{
  "success": true,
  "message": "全部成功：加载了 1 个文件",
  "loaded_files": ["/path/to/your/file.vtp"],
  "failed_files": [],
  "total_found": 1,
  "path_type": "file"
}
```

**文件夹加载成功：**
```json
{
  "success": true,
  "message": "全部成功：加载了 3 个文件",
  "loaded_files": [
    "/path/to/folder/file1.vtp",
    "/path/to/folder/file2.stl",
    "/path/to/folder/image.vti"
  ],
  "failed_files": [],
  "total_found": 3,
  "path_type": "folder"
}
```

**部分成功响应：**
```json
{
  "success": true,
  "message": "部分成功：加载了 2/3 个文件",
  "loaded_files": [
    "/path/to/folder/file1.vtp",
    "/path/to/folder/file2.stl"
  ],
  "failed_files": [
    {
      "path": "/path/to/folder/corrupted.vtp",
      "error": "文件格式错误"
    }
  ],
  "total_found": 3,
  "path_type": "folder"
}
```

#### 失败响应
```json
{
  "success": false,
  "message": "路径不存在: /invalid/path",
  "loaded_files": [],
  "failed_files": []
}
```

### HTTP状态码

- `200`: 请求成功
- `400`: 请求参数错误（缺少path参数）
- `500`: 服务器内部错误（文件不存在、加载失败等）

## 支持的文件格式

当前支持以下数据文件格式：
- `.vtp` - VTK PolyData文件
- `.stl` - STL表面网格文件
- `.vti` - VTK ImageData文件
- `.mha` - MetaImage文件
- `.nii` - NIfTI文件
- `.nii.gz` - 压缩的NIfTI文件
- `.nrrd` - NRRD文件

## 使用示例

### 1. 启动trame应用
```bash
cd /path/to/your/project
python examples/CTP4D.py
```

应用将在 `http://localhost:8080` 启动。

### 2. 从另一个服务调用API

#### Python示例

**加载单个文件：**
```python
import requests
import json

# 要加载的文件路径
file_path = "/data/medical/vessel.vtp"

# 发送POST请求
response = requests.post(
    "http://localhost:8080/api/load_data",
    json={"path": file_path},
    headers={'Content-Type': 'application/json'}
)

if response.status_code == 200:
    result = response.json()
    if result['success']:
        print(f"数据加载成功！加载了 {len(result['loaded_files'])} 个文件")
        for file in result['loaded_files']:
            print(f"  - {file}")
    else:
        print(f"加载失败: {result['message']}")
else:
    print(f"请求失败: {response.status_code}")
```

**加载文件夹：**
```python
import requests
import json

# 要加载的文件夹路径
folder_path = "/data/medical/scans/"

# 发送POST请求
response = requests.post(
    "http://localhost:8080/api/load_data",
    json={"path": folder_path},
    headers={'Content-Type': 'application/json'}
)

if response.status_code == 200:
    result = response.json()
    if result['success']:
        print(f"文件夹加载成功！")
        print(f"路径类型: {result['path_type']}")
        print(f"成功加载: {len(result['loaded_files'])} 个文件")
        print(f"失败文件: {len(result['failed_files'])} 个")

        # 显示加载的文件
        for file in result['loaded_files']:
            print(f"  ✅ {file}")

        # 显示失败的文件
        for failed in result['failed_files']:
            print(f"  ❌ {failed['path']}: {failed['error']}")
    else:
        print(f"加载失败: {result['message']}")
else:
    print(f"请求失败: {response.status_code}")
```

#### JavaScript示例
```javascript
const loadData = async (filePath) => {
  try {
    const response = await fetch('http://localhost:8080/api/load_data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ path: filePath })
    });

    const result = await response.json();

    if (response.ok && result.success) {
      console.log('数据加载成功！');
    } else {
      console.error('加载失败:', result.message);
    }
  } catch (error) {
    console.error('请求异常:', error);
  }
};

// 使用示例
loadData('/data/medical/vessel.vtp');
```

#### curl命令示例

**加载单个文件：**
```bash
# GET请求
curl "http://localhost:8080/api/load_data?path=/data/medical/vessel.vtp"

# POST请求
curl -X POST http://localhost:8080/api/load_data \
  -H "Content-Type: application/json" \
  -d '{"path": "/data/medical/vessel.vtp"}'
```

**加载文件夹：**
```bash
# GET请求
curl "http://localhost:8080/api/load_data?path=/data/medical/scans/"

# POST请求
curl -X POST http://localhost:8080/api/load_data \
  -H "Content-Type: application/json" \
  -d '{"path": "/data/medical/scans/"}'
```

### 3. 测试API

使用提供的测试脚本：
```bash
# 测试单个文件
python test_http_api.py /path/to/your/test/file.vtp

# 测试文件夹
python test_http_api.py /path/to/your/test/folder/
```

## 功能特性

### 🔥 核心功能
1. **文件和文件夹支持**: 既可以加载单个文件，也可以加载整个文件夹
2. **智能文件过滤**: 自动识别和加载支持的文件格式
3. **批量处理**: 一次请求处理多个文件
4. **数据清空**: 加载新数据前会自动清空当前所有数据节点

### 🛡️ 可靠性
5. **完善的错误处理**: 详细的错误信息和异常处理
6. **路径验证**: 自动验证文件/文件夹路径是否存在
7. **部分成功处理**: 即使部分文件失败，也会加载成功的文件
8. **详细反馈**: 提供加载成功和失败文件的详细列表

### 🌐 网络支持
9. **多种请求方式**: 支持GET和POST两种请求方式
10. **URL解码**: 支持URL编码的文件路径
11. **JSON响应**: 统一的JSON格式响应
12. **状态码规范**: 标准的HTTP状态码

## 注意事项

1. **文件路径**: 确保提供的文件路径在服务器上存在且可访问
2. **文件格式**: 只支持列出的数据文件格式
3. **网络安全**: 在生产环境中考虑添加认证和授权机制
4. **错误处理**: 调用方应该处理各种可能的错误情况
5. **并发访问**: 当前实现不支持并发数据加载操作

## 扩展功能

可以基于此实现进一步扩展：
- 添加用户认证
- 支持批量文件加载
- 添加数据加载进度反馈
- 支持更多文件格式
- 添加数据预览功能
