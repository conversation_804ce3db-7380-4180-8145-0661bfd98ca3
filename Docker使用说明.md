# Docker部署使用说明

## 🐳 概述

本文档介绍如何使用Docker Compose部署CTP4D可视化应用。

## 📋 前置要求

- Docker Engine 20.10+
- Docker Compose 1.29+
- 确保宿主机的8080端口未被占用
- 确保宿主机有`/data/ctpdata`目录（或根据需要修改路径）

## 🚀 快速启动

### 1. 启动服务
```bash
# 启动容器（后台运行）
docker-compose -f dev.yml up -d

# 启动容器（前台运行，可以看到日志）
docker-compose -f dev.yml up
```

### 2. 查看日志
```bash
# 查看实时日志
docker-compose -f dev.yml logs -f

# 查看最近的日志
docker-compose -f dev.yml logs --tail=100
```

### 3. 访问应用
打开浏览器访问：
- 主页：http://localhost:8080/
- 带数据加载：http://localhost:8080/?path=/data/ctpdata/your_file.vtp

### 4. 停止服务
```bash
# 停止容器
docker-compose -f dev.yml down

# 停止容器并删除数据卷
docker-compose -f dev.yml down -v
```

## 📁 目录结构

```
项目根目录/
├── dev.yml                 # Docker Compose配置文件
├── examples/
│   └── CTP4D.py            # 主应用文件
├── requirements.txt        # Python依赖
└── /data/ctpdata/         # 数据目录（宿主机）
    ├── vessel_001.vtp
    ├── vessel_002.vtp
    └── ...
```

## ⚙️ 配置说明

### 环境变量
- `PYTHONPATH=/app` - Python模块搜索路径
- `PYTHONUNBUFFERED=1` - 禁用Python输出缓冲
- `APP_HOST=0.0.0.0` - 应用监听地址
- `APP_PORT=8080` - 应用监听端口
- `DATA_PATH=/data/ctpdata` - 数据目录路径

### 数据卷挂载
- `.:/app` - 项目代码挂载到容器的/app目录
- `/data/ctpdata:/data/ctpdata` - 数据目录挂载

### 端口映射
- `8080:8080` - 宿主机8080端口映射到容器8080端口

## 🔧 自定义配置

### 修改数据目录
如果您的数据在其他位置，修改`dev.yml`中的volumes配置：
```yaml
volumes:
  - .:/app
  - /your/custom/data/path:/data/ctpdata  # 修改这里
```

### 修改端口
如果需要使用其他端口，修改`dev.yml`中的ports配置：
```yaml
ports:
  - "9090:8080"  # 宿主机9090端口映射到容器8080端口
```

### 添加额外的环境变量
```yaml
environment:
  - PYTHONPATH=/app
  - PYTHONUNBUFFERED=1
  - YOUR_CUSTOM_VAR=value
```

## 🛠️ 开发模式

### 代码热重载
由于项目目录挂载到容器中，您可以在宿主机上修改代码，然后重启容器：
```bash
# 重启容器
docker-compose -f dev.yml restart
```

### 进入容器调试
```bash
# 进入运行中的容器
docker exec -it ugs-ctp4d bash

# 在容器内执行命令
docker exec -it ugs-ctp4d python --version
```

## 📊 健康检查

容器配置了健康检查，会定期检查应用是否正常运行：
- 检查间隔：30秒
- 超时时间：10秒
- 重试次数：3次
- 启动等待：60秒

查看健康状态：
```bash
docker-compose -f dev.yml ps
```

## 🐛 故障排除

### 常见问题

#### 1. 端口被占用
```
Error: bind: address already in use
```
**解决方案：**
- 检查8080端口是否被占用：`netstat -tlnp | grep 8080`
- 修改dev.yml中的端口映射

#### 2. 数据目录不存在
```
Error: no such file or directory: '/data/ctpdata'
```
**解决方案：**
- 创建数据目录：`sudo mkdir -p /data/ctpdata`
- 或修改dev.yml中的数据目录路径

#### 3. 依赖安装失败
```
Error: Could not install packages due to an EnvironmentError
```
**解决方案：**
- 检查网络连接
- 检查requirements.txt文件是否存在
- 查看详细日志：`docker-compose -f dev.yml logs`

#### 4. 应用启动失败
**解决方案：**
- 查看容器日志：`docker-compose -f dev.yml logs ugs-ctp4d`
- 检查Python代码是否有语法错误
- 确保所有依赖都已正确安装

### 调试命令
```bash
# 查看容器状态
docker-compose -f dev.yml ps

# 查看容器详细信息
docker inspect ugs-ctp4d

# 查看网络信息
docker network ls
docker network inspect ctp4d-network

# 清理所有容器和网络
docker-compose -f dev.yml down --remove-orphans
```

## 🎯 使用示例

### 启动并加载数据
```bash
# 1. 准备数据
sudo mkdir -p /data/ctpdata
sudo cp your_data/*.vtp /data/ctpdata/

# 2. 启动服务
docker-compose -f dev.yml up -d

# 3. 访问应用并加载数据
# 浏览器访问：http://localhost:8080/?path=/data/ctpdata
```

### 批量处理
```bash
# 停止现有服务
docker-compose -f dev.yml down

# 更新代码
git pull

# 重新启动
docker-compose -f dev.yml up -d
```

## 📝 注意事项

1. **数据安全**：确保数据目录有适当的权限设置
2. **资源限制**：根据需要调整容器的CPU和内存限制
3. **网络安全**：在生产环境中考虑使用HTTPS和防火墙
4. **备份**：定期备份重要数据
5. **监控**：在生产环境中添加监控和日志收集

现在您可以使用Docker轻松部署和运行CTP4D可视化应用！🚀
