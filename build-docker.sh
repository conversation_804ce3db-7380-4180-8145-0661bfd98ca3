#!/bin/bash

# CTP4D Docker 构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 默认参数
IMAGE_NAME="harbor.unionstrongtech.com/ugs/ctp4d"
IMAGE_TAG="0.1.0"
DOCKERFILE="Dockerfile.fixed"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -f|--file)
            DOCKERFILE="$2"
            shift 2
            ;;
        -h|--help)
            echo "使用方法: $0 [选项]"
            echo "选项:"
            echo "  -t, --tag TAG     设置镜像标签 (默认: 0.1.0)"
            echo "  -n, --name NAME   设置镜像名称 (默认: harbor.unionstrongtech.com/ugs/ctp4d)"
            echo "  -f, --file FILE   指定Dockerfile (默认: Dockerfile.fixed)"
            echo "  -h, --help        显示帮助信息"
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            exit 1
            ;;
    esac
done

FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

print_info "开始构建Docker镜像"
echo "镜像名称: $FULL_IMAGE_NAME"
echo "Dockerfile: $DOCKERFILE"
echo

# 检查Dockerfile是否存在
if [ ! -f "$DOCKERFILE" ]; then
    print_error "Dockerfile不存在: $DOCKERFILE"
    exit 1
fi

# 检查必要文件
print_info "检查必要文件..."
if [ ! -f "requirements.txt" ]; then
    print_error "requirements.txt文件不存在"
    exit 1
fi

if [ ! -d "examples" ]; then
    print_error "examples目录不存在"
    exit 1
fi

if [ ! -f "examples/CTP4D.py" ]; then
    print_error "examples/CTP4D.py文件不存在"
    exit 1
fi

print_success "文件检查通过"

# 设置Docker构建参数，解决线程问题
export DOCKER_BUILDKIT=0
export BUILDKIT_PROGRESS=plain

print_info "开始构建镜像（使用优化的构建参数）..."

# 构建镜像，使用更多的构建参数来避免线程问题
if docker build \
    --file "$DOCKERFILE" \
    --tag "$FULL_IMAGE_NAME" \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    --memory=2g \
    --memory-swap=4g \
    --shm-size=1g \
    --ulimit nofile=65536:65536 \
    .; then
    
    print_success "镜像构建成功: $FULL_IMAGE_NAME"
    
    # 显示镜像信息
    print_info "镜像信息:"
    docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    echo
    print_info "使用方法:"
    echo "  # 运行容器"
    echo "  docker run -d -p 8080:8080 -v /data/ctpdata:/data/ctpdata $FULL_IMAGE_NAME"
    echo
    echo "  # 使用docker-compose"
    echo "  docker-compose -f prod.yml up -d"
    echo
    echo "  # 推送到仓库"
    echo "  docker push $FULL_IMAGE_NAME"
    
else
    print_error "镜像构建失败"
    echo
    print_info "故障排除建议:"
    echo "1. 检查网络连接是否正常"
    echo "2. 尝试清理Docker缓存: docker system prune -f"
    echo "3. 增加系统资源限制:"
    echo "   - 增加Docker内存限制"
    echo "   - 检查磁盘空间"
    echo "4. 使用备用构建方法:"
    echo "   - 使用原始Dockerfile: ./build-docker.sh -f Dockerfile"
    echo "   - 手动分步构建"
    
    exit 1
fi
