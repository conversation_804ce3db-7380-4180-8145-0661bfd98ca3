FROM python:3.8-slim

RUN apt-get update && \
    apt-get install -y \
        libx11-6 \
        libxrender1 \
        libxtst6 \
        libgl1-mesa-glx \
        libglib2.0-0 \
        python3-tk \
        tk-dev \
        libegl1 \
        libosmesa6 \
        libosmesa6-dev \
        libgles2-mesa-dev \
    || true && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

ENV OPENBLAS_NUM_THREADS=1 \
    NUMEXPR_NUM_THREADS=1 \
    MKL_NUM_THREADS=1 \
    OMP_NUM_THREADS=1 \
    VECLIB_MAXIMUM_THREADS=1 \
    NVIDIA_VISIBLE_DEVICES=all

COPY requirements.txt ./
COPY mipf/ ./mipf/
COPY examples/ ./examples/

RUN pip install --upgrade pip

RUN pip install --no-cache-dir numpy
RUN pip install --no-cache-dir -r requirements.txt

# 设置Python环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8080


