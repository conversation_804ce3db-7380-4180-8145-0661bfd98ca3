#!/usr/bin/env python3
"""
演示HTTP API数据加载功能的脚本
"""

import time
import subprocess
import sys
import os

def simulate_external_service():
    """模拟外部服务调用HTTP API"""
    print("🚀 模拟外部服务调用...")
    
    # 这里模拟另一个服务的HTTP调用
    # 在实际场景中，这可能是来自另一个Web应用、微服务等
    
    # 示例文件路径（请根据实际情况修改）
    test_files = [
        "/path/to/test/vessel.vtp",
        "/path/to/test/image.vti", 
        "/path/to/test/surface.stl"
    ]
    
    print("可以测试的文件路径示例：")
    for i, file_path in enumerate(test_files, 1):
        print(f"  {i}. {file_path}")
    
    print("\n💡 使用方法：")
    print("1. 启动trame应用：python examples/CTP4D.py")
    print("2. 在另一个终端运行：python test_http_api.py /your/file/path")
    print("3. 或者使用curl命令：")
    print("   curl 'http://localhost:8080/api/load_data?path=/your/file/path'")
    
    print("\n📝 支持的文件格式：")
    formats = [".vtp", ".stl", ".vti", ".mha", ".nii", ".nii.gz", ".nrrd"]
    print("   " + ", ".join(formats))

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    try:
        import requests
        print("✅ requests 库已安装")
    except ImportError:
        print("❌ requests 库未安装")
        print("   请运行: pip install requests")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 HTTP API 数据加载功能演示")
    print("=" * 60)
    
    print("\n📋 功能说明：")
    print("• 通过HTTP接口远程控制trame应用加载数据")
    print("• 支持GET和POST两种请求方式")
    print("• 自动清空现有数据并加载新数据")
    print("• 完善的错误处理和响应机制")
    
    print("\n🔧 实现原理：")
    print("• 使用trame框架的on_server_bind回调")
    print("• 添加自定义HTTP路由 /api/load_data")
    print("• 集成AppBase的数据管理功能")
    
    if not check_dependencies():
        return
    
    print("\n" + "=" * 60)
    simulate_external_service()
    print("=" * 60)
    
    print("\n🎉 演示完成！")
    print("现在您可以启动trame应用并测试HTTP API功能。")

if __name__ == "__main__":
    main()
