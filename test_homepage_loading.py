#!/usr/bin/env python3
"""
测试首页路径参数数据加载功能的脚本
"""

import requests
import time
import sys
import os
import tempfile
from urllib.parse import quote

def create_test_files():
    """创建测试文件"""
    temp_dir = tempfile.mkdtemp(prefix="homepage_test_")
    print(f"📁 创建测试文件夹: {temp_dir}")
    
    # 创建测试文件
    test_files = [
        "vessel_001.vtp",
        "vessel_002.vtp", 
        "brain_scan.vti",
        "surface_mesh.stl",
        "readme.txt"  # 不支持的格式
    ]
    
    for filename in test_files:
        file_path = os.path.join(temp_dir, filename)
        with open(file_path, 'w') as f:
            f.write(f"# Test file: {filename}\n")
        print(f"  ✅ 创建文件: {filename}")
    
    return temp_dir

def test_homepage_loading(base_url, test_path):
    """测试首页路径参数加载功能"""
    print(f"\n🏠 测试首页路径参数加载功能")
    print(f"服务器地址: {base_url}")
    print(f"测试路径: {test_path}")
    
    try:
        # 测试首页路径参数
        encoded_path = quote(test_path)
        homepage_url = f"{base_url}/?path={encoded_path}"
        
        print(f"\n📋 测试URL: {homepage_url}")
        
        response = requests.get(homepage_url, timeout=15)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容类型: {response.headers.get('content-type', 'unknown')}")
        print(f"响应长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            print("✅ 首页路径参数访问成功")
            print("   页面正常加载，数据应该已在后台加载")
            return True
        else:
            print(f"❌ 首页路径参数访问失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 首页路径参数测试异常: {str(e)}")
        return False

def test_normal_homepage(base_url):
    """测试正常首页访问（无路径参数）"""
    print(f"\n🌐 测试正常首页访问")
    
    try:
        response = requests.get(base_url, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容类型: {response.headers.get('content-type', 'unknown')}")
        
        if response.status_code == 200:
            print("✅ 正常首页访问成功")
            return True
        else:
            print(f"❌ 正常首页访问失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 正常首页测试异常: {str(e)}")
        return False

def test_api_compatibility(base_url, test_path):
    """测试API兼容性"""
    print(f"\n🔌 测试API兼容性")
    
    try:
        # 测试API接口是否仍然工作
        api_url = f"{base_url}/api/load_data"
        response = requests.post(
            api_url,
            json={'path': test_path},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API接口仍然正常工作")
            print(f"   加载的文件数量: {len(result.get('loaded_files', []))}")
            return True
        else:
            print(f"❌ API接口失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API兼容性测试异常: {str(e)}")
        return False

def cleanup_test_files(temp_dir):
    """清理测试文件"""
    import shutil
    try:
        shutil.rmtree(temp_dir)
        print(f"\n🧹 已清理测试文件夹: {temp_dir}")
    except Exception as e:
        print(f"⚠️  清理文件夹失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 首页路径参数数据加载功能测试")
    print("=" * 60)
    
    # 默认参数
    base_url = "http://localhost:8080"
    
    # 从命令行参数获取端口
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
            base_url = f"http://localhost:{port}"
        except ValueError:
            print(f"⚠️  无效的端口号: {sys.argv[1]}，使用默认端口8080")
    
    print(f"服务器地址: {base_url}")
    print(f"使用方法: python test_homepage_loading.py [port]")
    print()
    
    # 创建测试文件
    temp_dir = create_test_files()
    
    try:
        # 测试首页路径参数功能
        success1 = test_homepage_loading(base_url, temp_dir)
        
        # 等待一下让数据加载完成
        time.sleep(2)
        
        # 测试单个文件
        single_file = os.path.join(temp_dir, "vessel_001.vtp")
        success2 = test_homepage_loading(base_url, single_file)
        
        # 测试正常首页访问
        success3 = test_normal_homepage(base_url)
        
        # 测试API兼容性
        success4 = test_api_compatibility(base_url, temp_dir)
        
        # 总结结果
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print(f"   首页文件夹加载: {'✅ 通过' if success1 else '❌ 失败'}")
        print(f"   首页单文件加载: {'✅ 通过' if success2 else '❌ 失败'}")
        print(f"   正常首页访问: {'✅ 通过' if success3 else '❌ 失败'}")
        print(f"   API兼容性: {'✅ 通过' if success4 else '❌ 失败'}")
        
        if all([success1, success2, success3, success4]):
            print("\n🎉 所有测试通过！首页路径参数功能完全正常！")
        else:
            print("\n⚠️  部分测试失败，请检查服务器状态")
        
        print("\n💡 使用示例:")
        print(f"   文件夹加载: {base_url}/?path=/path/to/folder/")
        print(f"   单文件加载: {base_url}/?path=/path/to/file.vtp")
        print(f"   正常访问: {base_url}/")
        
    finally:
        # 清理测试文件
        cleanup_test_files(temp_dir)
    
    print("=" * 60)

if __name__ == "__main__":
    main()
