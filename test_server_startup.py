#!/usr/bin/env python3
"""
测试服务器启动和路由设置的脚本
用于验证修复后的代码是否能正常启动
"""

import sys
import os
import time
import threading
import subprocess

def test_server_startup():
    """测试服务器启动"""
    print("🚀 测试服务器启动...")
    
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            [sys.executable, "examples/CTP4D.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待几秒钟让服务器启动
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 服务器启动成功，进程正在运行")
            
            # 尝试终止进程
            process.terminate()
            process.wait(timeout=5)
            print("✅ 服务器已正常关闭")
            return True
        else:
            # 进程已经退出，获取错误信息
            stdout, stderr = process.communicate()
            print("❌ 服务器启动失败")
            print("标准输出:")
            print(stdout)
            print("错误输出:")
            print(stderr)
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        return False

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_modules = [
        'trame',
        'flask',
        'wslink'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} 已安装")
        except ImportError:
            print(f"❌ {module} 未安装")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n请安装缺失的模块:")
        for module in missing_modules:
            print(f"  pip install {module}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 服务器启动测试")
    print("=" * 60)
    
    # 检查依赖项
    if not check_dependencies():
        print("\n❌ 依赖项检查失败，请安装缺失的模块后重试")
        return
    
    print()
    
    # 测试服务器启动
    if test_server_startup():
        print("\n🎉 测试通过！服务器可以正常启动")
        print("\n💡 现在您可以:")
        print("1. 运行: python examples/CTP4D.py")
        print("2. 访问: http://localhost:8080")
        print("3. 测试API: python test_http_api.py /path/to/test/file")
    else:
        print("\n❌ 测试失败！请检查错误信息并修复问题")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
