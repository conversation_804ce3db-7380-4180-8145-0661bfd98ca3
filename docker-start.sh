#!/bin/bash

# CTP4D Docker 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    print_success "Docker环境检查通过"
}

# 检查端口是否被占用
check_port() {
    local port=8080
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口$port已被占用，请先停止占用该端口的进程"
        print_info "可以使用以下命令查看占用进程: lsof -i :$port"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查数据目录
check_data_dir() {
    local data_dir="/data/ctpdata"
    if [ ! -d "$data_dir" ]; then
        print_warning "数据目录 $data_dir 不存在"
        read -p "是否创建该目录？(Y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Nn]$ ]]; then
            print_info "您可以修改 dev.yml 中的数据目录路径"
            exit 1
        else
            sudo mkdir -p "$data_dir"
            sudo chmod 755 "$data_dir"
            print_success "已创建数据目录 $data_dir"
        fi
    else
        print_success "数据目录检查通过"
    fi
}

# 启动服务
start_service() {
    print_info "正在启动CTP4D服务..."
    
    # 停止可能存在的容器
    docker-compose -f dev.yml down 2>/dev/null || true
    
    # 启动容器
    if docker-compose -f dev.yml up -d; then
        print_success "服务启动成功"
    else
        print_error "服务启动失败"
        exit 1
    fi
}

# 等待服务就绪
wait_for_service() {
    print_info "等待服务启动..."
    local max_wait=60
    local wait_time=0
    
    while [ $wait_time -lt $max_wait ]; do
        if curl -s http://localhost:8080/ >/dev/null 2>&1; then
            print_success "服务已就绪"
            return 0
        fi
        
        sleep 2
        wait_time=$((wait_time + 2))
        echo -n "."
    done
    
    echo
    print_warning "服务启动可能需要更长时间，请稍后手动检查"
}

# 显示服务信息
show_service_info() {
    echo
    echo "🎉 CTP4D服务已启动！"
    echo
    echo "📋 服务信息:"
    echo "   - 容器名称: ugs-ctp4d"
    echo "   - 访问地址: http://localhost:8080/"
    echo "   - 数据目录: /data/ctpdata"
    echo
    echo "💡 使用示例:"
    echo "   - 普通访问: http://localhost:8080/"
    echo "   - 加载数据: http://localhost:8080/?path=/data/ctpdata/your_file.vtp"
    echo
    echo "🔧 管理命令:"
    echo "   - 查看日志: docker-compose -f dev.yml logs -f"
    echo "   - 停止服务: docker-compose -f dev.yml down"
    echo "   - 重启服务: docker-compose -f dev.yml restart"
    echo
}

# 主函数
main() {
    echo "🐳 CTP4D Docker 启动脚本"
    echo "=========================="
    echo
    
    # 检查环境
    check_docker
    check_port
    check_data_dir
    
    # 启动服务
    start_service
    
    # 等待服务就绪
    wait_for_service
    
    # 显示服务信息
    show_service_info
}

# 运行主函数
main "$@"
