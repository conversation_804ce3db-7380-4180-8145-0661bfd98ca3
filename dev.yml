version: '3.8'

services:
  ugs-ctp4d:
    image: harbor.unionstrongtech.com/ugs/ctp4d:0.1.0
    container_name: ugs-ctp4d
    working_dir: /app
    volumes:
      - /data/ctpdata:/data/ctpdata
    ports:
      - "8080:8080"
    command: python examples/CTP4D.py --server --host 0.0.0.0 --port 8080
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - APP_HOST=0.0.0.0
      - APP_PORT=8080
      - DATA_PATH=/data/ctpdata
    stdin_open: true
    tty: true
    restart: unless-stopped
