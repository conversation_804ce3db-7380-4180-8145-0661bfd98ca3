version: '3.8'

services:
  ugs-ctp4d:
    image: python:3.8-slim
    container_name: ugs-ctp4d
    working_dir: /app
    volumes:
      # 开发模式：挂载当前项目目录到容器（便于代码修改）
      - .:/app
      # 生产模式：只挂载数据目录（取消上面的代码挂载）
      # - /data/ctpdata:/data/ctpdata

      # 挂载数据目录
      - /data/ctpdata:/data/ctpdata
    ports:
      # 端口映射：宿主机8080 -> 容器8080
      - "8080:8080"
    command: >
      bash -c "
        echo '🐳 正在安装Python依赖...' &&
        pip install --no-cache-dir -r requirements.txt &&
        echo '🚀 启动CTP4D应用...' &&
        python examples/CTP4D.py --server --host 0.0.0.0 --port 8080
      "
    environment:
      # Python环境变量
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      # 应用配置
      - APP_HOST=0.0.0.0
      - APP_PORT=8080
      - DATA_PATH=/data/ctpdata
    stdin_open: true
    tty: true
    restart: unless-stopped
    networks:
      - ctp4d-network
    # 健康检查（可选，需要容器内有curl命令）
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:8080/"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 60s

networks:
  ctp4d-network:
    driver: bridge
    name: ctp4d-network

volumes:
  # 可选：如果需要持久化数据卷
  ctpdata:
    driver: local
