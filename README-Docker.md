# CTP4D Docker 部署

## 🚀 快速开始

### 方法1：使用启动脚本（推荐）
```bash
# 运行启动脚本
./docker-start.sh
```

### 方法2：手动启动
```bash
# 确保数据目录存在
sudo mkdir -p /data/ctpdata

# 启动服务
docker-compose -f dev.yml up -d

# 查看日志
docker-compose -f dev.yml logs -f
```

## 📁 文件说明

- `dev.yml` - Docker Compose配置文件
- `docker-start.sh` - 自动化启动脚本
- `test_docker_deployment.py` - Docker部署测试脚本
- `Docker使用说明.md` - 详细使用文档

## 🔧 配置

### 容器配置
- **基础镜像**: python:3.8-slim
- **容器名称**: ugs-ctp4d
- **工作目录**: /app
- **端口映射**: 8080:8080

### 数据目录
- **宿主机**: /data/ctpdata
- **容器内**: /data/ctpdata

### 启动命令
```bash
python examples/CTP4D.py --server --host 0.0.0.0 --port 8080
```

## 🌐 访问应用

- **主页**: http://localhost:8080/
- **加载数据**: http://localhost:8080/?path=/data/ctpdata/your_file.vtp

## 🛠️ 管理命令

```bash
# 启动服务
docker-compose -f dev.yml up -d

# 停止服务
docker-compose -f dev.yml down

# 重启服务
docker-compose -f dev.yml restart

# 查看日志
docker-compose -f dev.yml logs -f

# 查看容器状态
docker-compose -f dev.yml ps

# 进入容器
docker exec -it ugs-ctp4d bash
```

## 🧪 测试部署

```bash
# 运行自动化测试
python test_docker_deployment.py
```

## 📋 环境要求

- Docker Engine 20.10+
- Docker Compose 1.29+
- 端口8080未被占用

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8080
   
   # 修改端口映射
   # 编辑 dev.yml，将 "8080:8080" 改为 "9090:8080"
   ```

2. **数据目录权限**
   ```bash
   # 设置正确权限
   sudo chown -R $USER:$USER /data/ctpdata
   sudo chmod -R 755 /data/ctpdata
   ```

3. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose -f dev.yml logs
   
   # 重新构建
   docker-compose -f dev.yml up --build
   ```

## 📖 更多信息

详细使用说明请参考 [Docker使用说明.md](Docker使用说明.md)
